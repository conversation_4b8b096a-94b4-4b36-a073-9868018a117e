/* pages/user/center/center.wxss */
.container {
  min-height: 100vh;
  background: var(--primary-gradient);
  padding: var(--space-lg) var(--space-md) 0;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* 用户信息区域 */
.user-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-lg);
}

.user-card {
  padding: var(--space-xl);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-container {
  position: relative;
  margin-right: var(--space-lg);
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-full);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-md);
}

.avatar-ring {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  width: 132rpx;
  height: 132rpx;
  border-radius: var(--radius-full);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.05); opacity: 0.3; }
}

.user-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.welcome {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 会员状态区域 */
.membership-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-lg);
}

.membership-card {
  padding: var(--space-xl);
  transition: all var(--duration-normal) var(--ease-out);
}

.membership-card.member-active {
  background: linear-gradient(135deg, rgba(139, 182, 232, 0.3) 0%, rgba(168, 200, 237, 0.3) 100%);
  border: 1rpx solid rgba(139, 182, 232, 0.4);
}

.membership-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.membership-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.member-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: var(--space-md);
}

.member-detail {
  flex: 1;
}

.member-status {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.expire-time {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  display: block;
}

.permanent-badge {
  background: linear-gradient(45deg, var(--warning-color), var(--accent-color));
  color: var(--text-white);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
  margin-top: var(--space-xs);
  display: inline-block;
  box-shadow: var(--shadow-sm);
}

.member-benefits-preview {
  display: flex;
  align-items: center;
}

.benefits-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  backdrop-filter: blur(4rpx);
}

.member-benefits {
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  padding-top: var(--space-md);
  margin-top: var(--space-md);
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: var(--space-xs) 0;
}

.benefit-icon {
  color: var(--success-color);
  font-size: var(--text-sm);
  font-weight: 600;
  margin-right: var(--space-sm);
  width: 24rpx;
  text-align: center;
}

.benefit-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  flex: 1;
}

/* 支付按钮区域 */
.payment-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
  padding: 0 var(--space-md);
}

.payment-btn {
  width: 100%;
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.payment-btn.renew {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
}

.payment-btn:active {
  transform: scale(0.98);
}

.payment-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) var(--ease-out);
}

.payment-btn:active::before {
  left: 100%;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.btn-text {
  color: var(--text-white);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-xs);
}

.btn-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--text-sm);
}

.btn-icon {
  font-size: var(--text-2xl);
  opacity: 0.8;
}

/* 功能菜单区域 */
.menu-section {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  padding: 0 var(--space-md);
}

.main-menu-card,
.secondary-menu-card {
  padding: var(--space-xl);
}

.menu-title {
  margin-bottom: var(--space-lg);
  text-align: center;
}

.title-text {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.title-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.menu-grid {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.menu-grid .menu-item {
  flex: 1;
  margin-right: var(--space-lg);
}

.menu-grid .menu-item:last-child {
  margin-right: 0;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.menu-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.2);
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.menu-item:active::before {
  opacity: 1;
}

.menu-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-md);
  backdrop-filter: blur(8rpx);
}

.menu-emoji {
  font-size: var(--text-2xl);
}

.menu-text {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
  text-align: center;
}

/* 卡密兑换弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-out);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.redeem-modal {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1rpx solid var(--glass-border);
  border-radius: var(--radius-xl);
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform var(--duration-normal) var(--ease-out);
  box-shadow: var(--shadow-xl);
}

.modal-overlay.show .redeem-modal {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-xl);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  transition: opacity var(--duration-fast) var(--ease-out);
}

.close-btn:active {
  opacity: 1;
}

.modal-content {
  padding: var(--space-xl);
}

.purchase-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  margin-bottom: var(--space-xl);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.info-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-sm);
}

.info-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.redeem-form {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: var(--text-base);
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  font-weight: 500;
}

.card-input {
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  font-size: var(--text-base);
  margin-bottom: var(--space-xl);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  backdrop-filter: blur(4rpx);
  transition: border-color var(--duration-fast) var(--ease-out);
}

.card-input:focus {
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.2);
}

.redeem-submit-btn {
  background: var(--primary-gradient);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-lg);
  font-size: var(--text-base);
  font-weight: 600;
  transition: all var(--duration-normal) var(--ease-out);
  box-shadow: var(--shadow-md);
}

.redeem-submit-btn:active {
  transform: scale(0.98);
}

.redeem-submit-btn:disabled {
  background: var(--neutral-400);
  opacity: 0.6;
}

/* 底部占位 */
.tabbar-placeholder {
  height: calc(48px + env(safe-area-inset-bottom));
  margin-top: var(--space-xl);
}




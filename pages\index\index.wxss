.container {
  min-height: 100vh;
  background: var(--primary-gradient);
  padding: var(--space-xl) var(--space-md) calc(50px + var(--space-xl) + env(safe-area-inset-bottom)); /* 修改 */
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 250rpx;
  height: 250rpx;
  top: 5%;
  right: -80rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 180rpx;
  height: 180rpx;
  top: 70%;
  left: -60rpx;
  animation-delay: 3s;
}

.circle-3 {
  width: 120rpx;
  height: 120rpx;
  top: 40%;
  left: 60%;
  animation-delay: 6s;
}

/* 欢迎区域 */
.welcome-section {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.welcome-card {
  padding: var(--space-2xl);
  text-align: center;
}

.logo-container {
  position: relative;
  display: inline-block;
  margin-bottom: var(--space-lg);
}

.logo-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: var(--radius-xl);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-lg);
}

.logo-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 216rpx;
  height: 216rpx;
  border-radius: var(--radius-xl);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  animation: pulse 3s ease-in-out infinite;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.welcome-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.welcome-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 功能区域 */
.main-functions,
.secondary-functions {
  position: relative;
  z-index: 1;
  margin-bottom: var(--space-xl);
}

.functions-card {
  padding: var(--space-xl);
}

.card-title {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.title-text {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--space-xs);
}

.title-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.functions-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.function-item {
  display: flex;
  align-items: center;
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.function-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.2);
}

.function-item.primary {
  background: linear-gradient(135deg, rgba(139, 182, 232, 0.2) 0%, rgba(168, 200, 237, 0.2) 100%);
  border-color: rgba(139, 182, 232, 0.3);
}

.function-item.secondary {
  background: linear-gradient(135deg, rgba(212, 181, 160, 0.2) 0%, rgba(200, 168, 168, 0.2) 100%);
  border-color: rgba(212, 181, 160, 0.3);
}

.function-item.accent {
  background: linear-gradient(135deg, rgba(168, 196, 162, 0.2) 0%, rgba(232, 197, 160, 0.2) 100%);
  border-color: rgba(168, 196, 162, 0.3);
}

.function-item.success {
  background: linear-gradient(135deg, rgba(212, 165, 165, 0.2) 0%, rgba(168, 196, 162, 0.2) 100%);
  border-color: rgba(168, 196, 162, 0.3);
}

.function-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-lg);
  backdrop-filter: blur(8rpx);
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
}

.function-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.function-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.function-desc {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.function-badge {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-white);
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
}

.function-badge.free {
  background: linear-gradient(45deg, var(--success-color), #A8C4A2);
}

/* 底部占位 */
.tabbar-placeholder {
  height: calc(48px + env(safe-area-inset-bottom));
  margin-top: var(--space-xl);
}
